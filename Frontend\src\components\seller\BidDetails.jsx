import React from "react";
import { useParams } from "react-router-dom";
import { useSelector } from "react-redux";
import { selectBids } from "../../redux/slices/sellerDashboardSlice";
import SellerLayout from "./SellerLayout";
import Table from "../common/Table";
import "../../styles/BidDetails.css";
import { LuMessageSquareText } from "react-icons/lu";

const getBidDetails = (bidId, bids) => {
  const foundBid = bids.find(bid => bid.id === `#${bidId}`);

  if (foundBid) {
    return {
      ...foundBid,
      time: "4:50PM",
      customer: foundBid.customer || {
        name: foundBid.requestedCustomer || "<PERSON>",
        email: `<EMAIL>`,
        phone: "************"
      },
      history: [
        {
          bidId: foundBid.id,
          customer: foundBid.requestedCustomer || "<PERSON>",
          date: foundBid.date,
          price: foundBid.price,
          bidAmount: foundBid.bidAmount,
          status: "Pending",
          action: "View"
        },
        {
          bidId: "#BID1234",
          customer: "<PERSON>",
          date: "20 May 2024",
          price: "$25.00",
          bidAmount: "$22.00",
          status: "Accepted",
          action: "View"
        },
        {
          bidId: "#BID1235",
          customer: "Mike Johnson",
          date: "19 May 2024",
          price: "$25.00",
          bidAmount: "$20.00",
          status: "Rejected",
          action: "View"
        },
        {
          bidId: "#BID1236",
          customer: "Sarah Wilson",
          date: "18 May 2024",
          price: "$25.00",
          bidAmount: "$23.00",
          status: "Counter Offer",
          action: "View"
        },
        {
          bidId: "#BID1237",
          customer: "David Brown",
          date: "17 May 2024",
          price: "$25.00",
          bidAmount: "$21.00",
          status: "Pending",
          action: "View"
        },
        {
          bidId: "#BID1238",
          customer: "Lisa Davis",
          date: "16 May 2024",
          price: "$25.00",
          bidAmount: "$24.00",
          status: "Accepted",
          action: "View"
        }
      ]
    };
  }

  // Default bid details if not found
  return {
    id: `#${bidId}`,
    title: "Frank Martin - Drills And Coaching Philosophies To Developing Toughness In Your Players",
    subtitle: "Basketball Coaching Clinic",
    image: "https://images.unsplash.com/photo-1546519638-68e109498ffc?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1000&q=80",
    date: "21 May 2024",
    time: "4:50PM",
    price: "$25.00",
    bidAmount: "$22.00",
    customer: {
      name: "John Smith",
      email: "<EMAIL>",
      phone: "************"
    },
    history: [
      {
        bidId: `#${bidId}`,
        customer: "John Smith",
        date: "21 May 2024",
        price: "$25.00",
        bidAmount: "$22.00",
        status: "Pending",
        action: "View"
      },
      {
        bidId: "#BID1234",
        customer: "Jane Doe",
        date: "20 May 2024",
        price: "$25.00",
        bidAmount: "$22.00",
        status: "Accepted",
        action: "View"
      },
      {
        bidId: "#BID1235",
        customer: "Mike Johnson",
        date: "19 May 2024",
        price: "$25.00",
        bidAmount: "$20.00",
        status: "Rejected",
        action: "View"
      },
      {
        bidId: "#BID1236",
        customer: "Sarah Wilson",
        date: "18 May 2024",
        price: "$25.00",
        bidAmount: "$23.00",
        status: "Counter Offer",
        action: "View"
      },
      {
        bidId: "#BID1237",
        customer: "David Brown",
        date: "17 May 2024",
        price: "$25.00",
        bidAmount: "$21.00",
        status: "Pending",
        action: "View"
      },
      {
        bidId: "#BID1238",
        customer: "Lisa Davis",
        date: "16 May 2024",
        price: "$25.00",
        bidAmount: "$24.00",
        status: "Accepted",
        action: "View"
      }
    ]
  };
};

const BidDetails = () => {
  const { id } = useParams();
  const bids = useSelector(selectBids);
  const bid = getBidDetails(id, bids);

  const historyColumns = [
    { key: "bidId", label: "Bid Id" },
    { key: "customer", label: "Customer" },
    { key: "date", label: "Date" },
    { key: "price", label: "Price" },
    { key: "bidAmount", label: "Bid Amount" },
    {
      key: "status",
      label: "Status",
      render: (item) => (
        <span className={`status-badge status-${item.status.toLowerCase().replace(' ', '-')}`}>
          {item.status}
        </span>
      ),
    },
    { key: "action", label: "Action" },
  ];

  if (!bid) {
    return (
      <SellerLayout>
        <div className="BidDetails">
          <div className="BidDetails__error">
            <p>Bid not found</p>
          </div>
        </div>
      </SellerLayout>
    );
  }

  return (
    <SellerLayout>
      <div className="BidDetails">
        <div className="BidDetails__content">
          <div className="BidDetails__main-section">
            <div className="BidDetails__header">
              <div className="BidDetails__content-info">
                <img
                  src={bid.image}
                  alt={bid.title}
                  className="BidDetails__content-image"
                />
                <div className="BidDetails__content-details">
                  <h3 className="BidDetails__content-title">{bid.title}</h3>
                  <p className="BidDetails__content-subtitle">{bid.subtitle}</p>
                </div>
              </div>
            </div>

            <div className="BidDetails__info-grid">
              <h3 className="BidDetails__section-title">Bid Information</h3>
              <div className="BidDetails__info-section">
                <div className="BidDetails__info-item-grid">
                  <div className="BidDetails__info-item">
                    <span className="BidDetails__info-label">Bid Id</span>
                    <span className="BidDetails__info-value">{bid.id}</span>
                  </div>
                  <div className="BidDetails__info-item">
                    <span className="BidDetails__info-label">Date</span>
                    <span className="BidDetails__info-value">{bid.date} | {bid.time}</span>
                  </div>
                  <div className="BidDetails__info-item">
                    <span className="BidDetails__info-label">Price</span>
                    <span className="BidDetails__info-value">{bid.price}</span>
                  </div>
                  <div className="BidDetails__info-item">
                    <span className="BidDetails__info-label">Bid Amount</span>
                    <span className="BidDetails__info-value">{bid.bidAmount}</span>
                  </div>
                </div>
                
                <div className="vertical-line"></div>
                
                <div className="BidDetails__info-item-grid">
                  <h3 className="BidDetails__section-title">Customer Details</h3>
                  <div className="BidDetails__info-item">
                    <span className="BidDetails__info-label">Email Address</span>
                    <span className="BidDetails__info-value">{bid.customer.email}</span>
                  </div>
                  <div className="BidDetails__info-item">
                    <span className="BidDetails__info-label">Phone Number</span>
                    <span className="BidDetails__info-value">{bid.customer.phone}</span>
                  </div>
                </div>
                
                <div className="vertical-line"></div>
                
                <div className="BidDetails__info-item-grid bidDetails-btn-grid">
                  <button className="btn-outline">
                    <LuMessageSquareText /> Message
                  </button>
                </div>
              </div>
            </div>
          </div>
          
          <div className="BidDetails__actions">
            <button className="BidDetails__btn BidDetails__btn--accept">
              Accepted
            </button>
            <button className="BidDetails__btn btn-primary">
              Rejected
            </button>
          </div>
          
          <div className="BidDetails__history-section">
            <h3 className="BidDetails__section-title">History</h3>
            <Table
              columns={historyColumns}
              data={bid.history}
              className="BidDetails__history-table"
            />
          </div>
        </div>
      </div>
    </SellerLayout>
  );
};

export default BidDetails;
