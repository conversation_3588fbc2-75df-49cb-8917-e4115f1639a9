import React, { useState } from 'react';
import SellerOnboardingStep1 from './SellerOnboardingStep1';

// Test component to demonstrate the dynamic experience management functionality
const ExperienceManagementTest = () => {
  const [formData, setFormData] = useState({
    description: 'Test description',
    profilePic: null,
    experiences: [
      { schoolName: 'University A', position: 'Coach', fromYear: '2020', toYear: '2022' },
      { schoolName: 'University B', position: 'Trainer', fromYear: '2018', toYear: '2020' }
    ]
  });

  const [fieldErrors, setFieldErrors] = useState({});

  const handleInputChange = (field, value) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));
  };

  const handleExperienceChange = (index, field, value) => {
    setFormData(prev => ({
      ...prev,
      experiences: prev.experiences.map((exp, idx) =>
        idx === index ? { ...exp, [field]: value } : exp
      )
    }));
  };

  const addExperience = () => {
    setFormData(prev => ({
      ...prev,
      experiences: [...prev.experiences, { schoolName: '', position: '', fromYear: '', toYear: '' }]
    }));
  };

  const removeExperience = (index) => {
    if (formData.experiences.length > 1) {
      setFormData(prev => ({
        ...prev,
        experiences: prev.experiences.filter((_, idx) => idx !== index)
      }));
    }
  };

  const handleNext = () => {
    console.log('Next clicked with data:', formData);
  };

  return (
    <div style={{ padding: '20px', maxWidth: '800px', margin: '0 auto' }}>
      <h2>Experience Management Test</h2>
      <p>Current experiences count: {formData.experiences.length}</p>
      <p>Delete buttons visible: {formData.experiences.length > 1 ? 'Yes' : 'No'}</p>
      
      <SellerOnboardingStep1
        formData={formData}
        onInputChange={handleInputChange}
        onExperienceChange={handleExperienceChange}
        onAddExperience={addExperience}
        onRemoveExperience={removeExperience}
        onNext={handleNext}
        fieldErrors={fieldErrors}
      />
      
      <div style={{ marginTop: '20px', padding: '10px', backgroundColor: '#f5f5f5', borderRadius: '5px' }}>
        <h3>Current Form Data:</h3>
        <pre>{JSON.stringify(formData, null, 2)}</pre>
      </div>
    </div>
  );
};

export default ExperienceManagementTest;
